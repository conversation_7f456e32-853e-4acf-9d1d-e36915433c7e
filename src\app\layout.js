import '../styles/globals.css'
import Header from '../components/Header'
import Footer from '../components/Footer'
import ChatbotProvider from '../components/ChatbotProvider'
import ServiceWorkerRegistration from '../components/ServiceWorkerRegistration'
import PerformanceMonitor from '../components/PerformanceMonitor'
import WhatsAppFloatingButton from '../components/WhatsAppFloatingButton'
import { AuthProvider } from '../contexts/AuthContext'

export const metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  title: 'Top Engineering Colleges in Bangalore | College Comparison & Rankings',
  description: 'Compare top engineering colleges in Bangalore. Get detailed information about placements, courses, rankings, and campus facilities. Find your perfect engineering college match.',
  keywords: 'engineering colleges bangalore, college comparison, placement statistics, RVCE, MSRIT, PES University, BMS College, college rankings',
  authors: [{ name: 'College Comparison Platform' }],
  openGraph: {
    title: 'Top Engineering Colleges in Bangalore | College Comparison',
    description: 'Compare top engineering colleges in Bangalore with detailed placement statistics, course information, and campus facilities.',
    type: 'website',
    locale: 'en_IN',
    siteName: 'Bangalore Engineering Colleges',
  },
  twitter: {
    card: 'summary',
    title: 'Top Engineering Colleges in Bangalore',
    description: 'Compare engineering colleges in Bangalore with detailed insights.',
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#2563eb" />
        
        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "Bangalore Engineering Colleges Comparison",
              "description": "Compare top engineering colleges in Bangalore with detailed placement statistics and course information",
              "url": "https://bangalore-engineering-colleges.com",
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://bangalore-engineering-colleges.com/colleges?search={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
      </head>
      <body className="min-h-screen bg-gray-50 flex flex-col">
        <AuthProvider>
          <Header />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />

        {/* Performance Monitoring */}
        <PerformanceMonitor />

        {/* Service Worker Registration */}
        <ServiceWorkerRegistration />

        {/* Chatbot Integration */}
        <ChatbotProvider />

          {/* Enhanced WhatsApp Floating Button */}
          <WhatsAppFloatingButton />
        </AuthProvider>
      </body>
    </html>
  )
}
